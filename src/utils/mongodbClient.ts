import { MongoDBClientResponse, ProductInfo } from "@/types/mongodb"

/**
 * MongoDB客户端工具类
 */
export class MongoDBClient {
  private baseUrl: string

  constructor(baseUrl: string = "/api/mongodb") {
    this.baseUrl = baseUrl
  }

  /**
   * 根据用户ID获取查询列表
   * @param userId 用户ID
   * @returns 查询列表
   */
  async getQueriesByUserId(userId: string): Promise<string[]> {
    const response = await fetch(this.baseUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "getQueries",
        userId,
      }),
    })

    const result: MongoDBClientResponse<string[]> = await response.json()

    if (!result.success) {
      throw new Error(result.error || "Failed to fetch queries")
    }

    return result.data || []
  }

  /**
   * 根据用户ID获取文档ID列表
   * @param userId 用户ID
   * @returns 文档ID列表
   */
  async getDocumentIdsByUserId(userId: string): Promise<string[]> {
    const response = await fetch(this.baseUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "getDocumentIds",
        userId,
      }),
    })

    const result: MongoDBClientResponse<string[]> = await response.json()

    if (!result.success) {
      throw new Error(result.error || "Failed to fetch document IDs")
    }

    return result.data || []
  }

  /**
   * 根据ID列表获取产品信息
   * @param ids ID列表，格式为"{product_id}-{host}"
   * @returns 产品信息列表
   */
  async getProductInfoByIds(ids: string[]): Promise<ProductInfo[]> {
    const response = await fetch(this.baseUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "getProductInfo",
        ids,
      }),
    })

    const result: MongoDBClientResponse<ProductInfo[]> = await response.json()

    if (!result.success) {
      throw new Error(result.error || "Failed to fetch product info")
    }

    return result.data || []
  }
}

// 默认客户端实例
export const mongodbClient = new MongoDBClient()

// 便捷函数
export const getQueriesByUserId = (userId: string) =>
  mongodbClient.getQueriesByUserId(userId)

export const getDocumentIdsByUserId = (userId: string) =>
  mongodbClient.getDocumentIdsByUserId(userId)

export const getProductInfoByIds = (ids: string[]) =>
  mongodbClient.getProductInfoByIds(ids)
