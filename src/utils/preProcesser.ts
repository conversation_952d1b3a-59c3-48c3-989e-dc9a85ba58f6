"use server"

import {
  getDocumentIdsByUserId,
  getProductInfoByIds,
  getQueriesByUserId,
} from "./mongodb"

export async function inputProcesser(message: string) {
  const queryList = await getQueriesByUserId(message)
  console.log("---- qs", queryList)

  const pidList = await getDocumentIdsByUserId(message)

  const pList = await getProductInfoByIds(pidList)

  const q = {
    queries: queryList,
    products: pList.map((it) => ({
      brand: it.brand,
      description: it.title,
      categories: it.categories?.[0],
      platform: it.host,
    })),
  }
  console.log("---- q", q)

  return JSON.stringify(q)
}

export async function outputProcesser(response: string) {
  console.log("---- response", response)

  // Try to find JSO<PERSON> in the response
  const jsonMatch = response.match(/\{[\s\S]*\}/)

  if (jsonMatch) {
    const jsonStr = jsonMatch[0]
    const parsed = JSON.parse(jsonStr)

    // Log the parsed JSON for debugging
    console.log("Detected JSON in response:", jsonStr)

    // If there's an output field, return it; otherwise return original response
    if (parsed.output) {
      return `描述列表：
      - [一句话概括(主观视角)]:

        ${parsed.output.bioSubjective}

      - [一句话概括(客观视角)]:

        ${parsed.output.bioObjective} 

      - [社媒风格描述]:

        ${parsed.output.descriptionSocial}

      - [闺蜜风格描述]:

        ${parsed.output.descriptionFriend}
      `
    }
  }

  // If no JSON found or no output field, return original response
  return response
}
