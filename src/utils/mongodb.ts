import { Collection, Db, MongoClient } from "mongodb"

import {
  COLLECTIONS,
  GemTaskResponseDocument,
  isValidQuery,
  ProductInfo,
  UserSavedCollectionsDocument,
} from "@/types/mongodb"

// MongoDB连接实例
let client: MongoClient | null = null
let db: Db | null = null

/**
 * 获取MongoDB连接
 */
async function getDatabase(): Promise<Db> {
  if (!db) {
    const mongoUrl = process.env.MONGODB_CONN
    if (!mongoUrl) {
      throw new Error("MongoDB URL not found in environment variables")
    }

    client = new MongoClient(mongoUrl)
    await client.connect()
    db = client.db("copilot")
  }
  return db
}

/**
 * 关闭MongoDB连接
 */
export async function closeConnection(): Promise<void> {
  if (client) {
    await client.close()
    client = null
    db = null
  }
}

/**
 * 根据user_id读取gem_task_response集合，提取query字段
 * @param userId 用户ID
 * @returns query列表，过滤掉无效query
 */
export async function getQueriesByUserId(userId: string): Promise<string[]> {
  try {
    const database = await getDatabase()
    const collection: Collection<GemTaskResponseDocument> = database.collection(
      COLLECTIONS.GEM_TASK_RESPONSE,
    )

    // 查询匹配user_id的文档
    const documents = await collection.find({ user_id: userId }).toArray()

    // 提取query字段并过滤无效值
    const queries: string[] = []

    for (const doc of documents) {
      if (
        doc.query &&
        typeof doc.query === "string" &&
        isValidQuery(doc.query)
      ) {
        queries.push(doc.query)
      }
    }

    return queries
  } catch (error) {
    console.error("Error fetching queries by user_id:", error)
    throw error
  }
}

/**
 * 根据user_id读取user_saved_collections集合，提取document_id字段
 * @param userId 用户ID
 * @returns document_id列表
 */
export async function getDocumentIdsByUserId(
  userId: string,
): Promise<string[]> {
  try {
    const database = await getDatabase()
    const collection: Collection<UserSavedCollectionsDocument> =
      database.collection(COLLECTIONS.USER_SAVED_COLLECTIONS)

    // 查询匹配user_id且collection_type为product的文档
    const documents = await collection
      .find({
        user_id: userId,
        collection_type: "product",
      })
      .toArray()

    // 提取document_id字段
    const documentIds: string[] = []

    for (const doc of documents) {
      if (doc.document_id && typeof doc.document_id === "string") {
        documentIds.push(doc.document_id)
      }
    }

    return documentIds
  } catch (error) {
    console.error("Error fetching document_ids by user_id:", error)
    throw error
  }
}

/**
 * 根据ID列表获取产品信息
 * @param ids ID列表，格式为"{product_id}-{host}"
 * @returns 产品信息列表
 */
export async function getProductInfoByIds(
  ids: string[],
): Promise<ProductInfo[]> {
  try {
    // const database = await getDatabase()
    // const collection: Collection<DealInfoDocument> = database.collection(
    //   COLLECTIONS.GEM_TASK_PRODUCT_LIST,
    // )

    const results = await Promise.allSettled(
      ids.map(async (id) => {
        const i = id.lastIndexOf("-")
        if (i === -1) throw new Error(`Invalid ID: ${id}`)

        const res = await fetch(
          `https://gem-workflow.favie.yesy.online/product/${id}`,
          {
            cache: "no-store",
          },
        )
        if (!res.ok) throw new Error(`HTTP ${res.status} for ${id}`)

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const doc: any = await res.json()
        if (!doc) return null

        const info: ProductInfo = {
          product_id: doc.global_id || "",
          host: doc.platform || "",
          brand: doc.brand || "",
          title: doc.title || "",
          categories: [doc.tags?.cate_tag].filter(Boolean),
        }
        return info
      }),
    )

    const productInfos = results
      .filter(
        (r): r is PromiseFulfilledResult<ProductInfo | null> =>
          r.status === "fulfilled",
      )
      .map((r) => r.value)
      .filter((v): v is ProductInfo => v !== null)

    // const productInfos: ProductInfo[] = []

    // for (const id of ids) {
    //   // 解析ID格式 "{product_id}-{host}"
    //   const lastDashIndex = id.lastIndexOf("-")
    //   if (lastDashIndex === -1) {
    //     console.warn(`Invalid ID format: ${id}`)
    //     continue
    //   }

    //   const productId = id.substring(0, lastDashIndex)
    //   const host = id.substring(lastDashIndex + 1)

    //   // 查询匹配product_id和host的文档
    //   // const document = await collection.findOne({
    //   //   product_id: productId,
    //   //   host: { $regex: host, $options: "i" },
    //   // })

    //   const document = (
    //     await fetch(`https://gem-workflow.favie.yesy.online/product/${id}`)
    //   )
    //     // eslint-disable-next-line @typescript-eslint/no-explicit-any
    //     .json() as any

    //   if (document) {
    //     const productInfo: ProductInfo = {
    //       host: document.host || "",
    //       product_id: document.product_id || "",
    //       brand: document.brand || "",
    //       title: document.title || "",
    //       categories: Array.isArray(document.categories)
    //         ? document.categories
    //         : [],
    //     }
    //     productInfos.push(productInfo)
    //   }
    // }

    return productInfos
  } catch (error) {
    console.error("Error fetching product info by ids:", error)
    throw error
  }
}
